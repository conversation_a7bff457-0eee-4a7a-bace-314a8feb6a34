/*
 * File: app/view/ProtocolProtocolNewWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ProtocolProtocolNewWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ProtocolProtocolNewWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.Hidden',
        'Ext.form.field.Display',
        'Ext.form.field.Date',
        'Ext.form.RadioGroup',
        'Ext.form.field.Radio',
        'Ext.form.field.ComboBox',
        'Ext.form.field.TextArea',
        'Ext.toolbar.Spacer',
        'Ext.tab.Panel',
        'Ext.grid.Panel',
        'Ext.grid.View',
        'Ext.toolbar.Toolbar',
        'Ext.toolbar.Fill',
        'Ext.tab.Tab',
        'Ext.grid.column.Action',
        'Ext.form.field.File',
        'Ext.grid.column.Date'
    ],

    permissible: true,
    id: 'ProtocolProtocolNewWin',
    itemId: 'ProtocolProtocolNewWin',
    width: 600,
    resizable: false,
    title: 'Creazione Protocollo',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    saveProtocol: function(forced, data, fromMail) {
                        var store = Ext.getStore('ProtocolProtocols'),
                            newProtocol = data.id === "",
                            a = 'aggiunto',
                            empty = true,
                            fromMail = fromMail ? fromMail : false;

                        if(typeof data.force_period_warning != 'undefined'){
                            store.getProxy().setExtraParam('force_period_warning', true);
                        }
                        Ext.getCmp('ProtocolProtocolNewWin').setLoading();
                        // Saves the data
                        if (!newProtocol) {
                            a = 'aggiornato';
                            var record = Ext.getCmp('ProtocolsGrid').getSelectionModel().getSelection()[0];

                            record.set('reserved', data.reserved);
                            record.set('date', data.date);
                            record.set('description', data.description);
                            record.set('direction', data.direction);
                            record.set('type_id', data.type_id);
                            record.set('subject_kind_id', data.subject_kind_id);
                            record.set('send_method_id', data.send_method_id);
                            record.set('note', data.note);
                            record.set('external_act_number', data.external_act_number);
                            record.set('dossier', data.dossier);
                            record.set('linked_correspondents', data.linked_correspondents);
                            record.set('linked_documents', data.linked_documents);
                            record.set('linked_protocols', data.linked_protocols);
                            record.set('header_position', data.header_position);
                        } else {
                            store.add(data);
                        }

                        // Sets forced values
                        for (var item in forced)
                        {
                            empty = false;
                            break;
                        }

                        if (!empty) {
                            store.getProxy().setExtraParam('forced', Ext.JSON.encode(forced));
                        }


                        // Syncs the record
                        store.sync({
                            callback: function() {
                                store.load();
                                Ext.getStore('ProtocolTypesTree').load();
                                Ext.getStore('ArchiveDocumentsArchived').load();
                                Ext.getCmp('ProtocolProtocolNewWin').setLoading(false);
                            },
                            success: function(form, action) {
                                res = Ext.decode(form.operations[0].response.responseText);
                                num = res.results.protocol_number;
                                if (!newProtocol) {
                                    Ext.getCmp('ProtocolsGrid').getSelectionModel().deselectAll();
                                    Ext.getCmp('ProtocolProtocolNewWin').close();
                                    Ext.Msg.alert('Successo', 'Protocollo n.' + num + ' ' + a);
                                } else {
                                    if(Ext.getCmp('ProtocolProtocolNewWin').fromDocument !== true){
                                        Ext.Msg.show({
                                            title: 'Successo',
                                            msg: 'Protocollo n.' + num + ' ' + a + '.<br/>Precompilare un nuovo protocollo sulla base del precedente?',
                                            buttons: Ext.Msg.YESNO,
                                            fn: function(r) {
                                                if (r == 'yes') {
                                                    Ext.getStore('ProtocolLinkedCorrespondentsForm').removeAll();
                                                    Ext.getStore('ProtocolLinkedDocumentsForm').removeAll();
                                                    Ext.getStore('ProtocolLinkedProtocolsForm').removeAll();
                                                    Ext.getCmp('ProtocolNewDescription').setValue();
                                                } else {
                                                    Ext.getCmp('ProtocolProtocolNewWin').close();
                                                }
                                            }
                                        });
                                    } else {
                                        Ext.Msg.alert('CONFERMA', 'Protocollo salvato con successo');

                                        if(fromMail === true) {
                                            var r = Ext.getCmp('ProtocolProtocolNewWin').record;
                                            console.log(r);
                                            Ext.Ajax.request({
                                                url: '/mc2-api/archive/document/' + r.get('id'),
                                                method:'PUT',
                                                params: {
                                                    completed: 1
                                                }
                                            });
                                            Ext.getCmp('ArchiveDocumentUploadWin').close();
                                        }


                                        Ext.getCmp('ProtocolProtocolNewWin').close();

                                    }
                                    Ext.getStore('ArchiveDocumentsUser').load();
                                    Ext.getStore('ArchiveDocumentsOffice').load();
                                }

                            },
                            failure: function(form, action) {
                                var res = form.proxy.getReader().jsonData,
                                    msg = res.message;

                                if (res.warning){
                                    Ext.MessageBox.show({
                                        title:'Attenzione',
                                        msg: msg,
                                        buttons: Ext.MessageBox.YESNO,
                                        fn: function(r){
                                            if (r == 'yes'){
                                                data.force_period_warning = true;
                                                Ext.getCmp('ProtocolNewForm').saveProtocol([], data
                                                );
                                                return;
                                            }
                                        }
                                    });
                                } else {
                                    if(res.status === 0){
                                        Ext.Msg.alert('Attenzione', 'Protocollo NON ' + a);
                                    } else {
                                        Ext.Msg.alert('Attenzione', res.message);
                                    }

                                }
                            }
                        });
                    },
                    border: false,
                    id: 'ProtocolNewForm',
                    itemId: 'ProtocolNewForm',
                    autoScroll: true,
                    bodyCls: 'bck-content',
                    bodyPadding: 10,
                    header: false,
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'hiddenfield',
                            id: 'ProtocolNewId',
                            itemId: 'ProtocolNewId',
                            fieldLabel: 'Label',
                            name: 'id'
                        },
                        {
                            xtype: 'container',
                            flex: 1,
                            layout: {
                                type: 'hbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'displayfield',
                                    flex: 1,
                                    id: 'ProtocolNewNumber',
                                    itemId: 'ProtocolNewNumber',
                                    fieldLabel: 'Numero',
                                    labelAlign: 'right',
                                    name: 'protocol_number',
                                    value: '-'
                                },
                                {
                                    xtype: 'datefield',
                                    flex: 1,
                                    id: 'ProtocolNewDate',
                                    itemId: 'ProtocolNewDate',
                                    disabled: true,
                                    maxWidth: 200,
                                    fieldLabel: 'Data',
                                    labelAlign: 'right',
                                    name: 'date',
                                    allowBlank: false,
                                    allowOnlyWhitespace: false,
                                    editable: false,
                                    format: 'd/m/Y',
                                    startDay: 1,
                                    submitFormat: 'c'
                                }
                            ]
                        },
                        {
                            xtype: 'checkboxfield',
                            hidden: true,
                            id: 'ProtocolNewReserved',
                            itemId: 'ProtocolNewReserved',
                            fieldLabel: 'Riservato',
                            name: 'reserved',
                            uncheckedValue: 'off'
                        },
                        {
                            xtype: 'radiogroup',
                            flex: 1,
                            id: 'ProtocolNewDirection',
                            itemId: 'ProtocolNewDirection',
                            items: [
                                {
                                    xtype: 'radiofield',
                                    name: 'direction',
                                    boxLabel: 'Interno',
                                    checked: true,
                                    inputValue: 'L'
                                },
                                {
                                    xtype: 'radiofield',
                                    name: 'direction',
                                    boxLabel: 'Entrante',
                                    inputValue: 'I'
                                },
                                {
                                    xtype: 'radiofield',
                                    name: 'direction',
                                    boxLabel: 'Uscente',
                                    inputValue: 'O'
                                }
                            ]
                        },
                        {
                            xtype: 'combobox',
                            flex: 1,
                            id: 'ProtocolNewType',
                            itemId: 'ProtocolNewType',
                            fieldLabel: 'Voce Titolario*',
                            labelAlign: 'right',
                            name: 'type_id',
                            anyMatch: true,
                            displayField: 'full_denomination',
                            forceSelection: true,
                            queryMode: 'local',
                            store: 'ProtocolTypesLeaf',
                            typeAhead: true,
                            valueField: 'id'
                        },
                        {
                            xtype: 'container',
                            flex: 1,
                            layout: {
                                type: 'hbox',
                                align: 'stretch',
                                padding: '0 0 5 0'
                            },
                            items: [
                                {
                                    xtype: 'combobox',
                                    flex: 1,
                                    id: 'ProtocolNewSubjectKind',
                                    itemId: 'ProtocolNewSubjectKind',
                                    padding: '0 5 0 0',
                                    fieldLabel: 'Tipo Oggetto',
                                    labelAlign: 'right',
                                    name: 'subject_kind_id',
                                    editable: false,
                                    matchFieldWidth: false,
                                    displayField: 'title',
                                    forceSelection: true,
                                    queryMode: 'local',
                                    store: 'ProtocolSubjectKinds',
                                    valueField: 'id'
                                },
                                {
                                    xtype: 'combobox',
                                    flex: 1,
                                    id: 'ProtocolNewSendMethod',
                                    itemId: 'ProtocolNewSendMethod',
                                    maxWidth: 280,
                                    padding: '0 0 0 5',
                                    fieldLabel: 'Metodo Invio',
                                    labelAlign: 'right',
                                    name: 'send_method_id',
                                    editable: false,
                                    matchFieldWidth: false,
                                    displayField: 'title',
                                    forceSelection: true,
                                    queryMode: 'local',
                                    store: 'ProtocolSendMethods',
                                    valueField: 'id'
                                }
                            ]
                        },
                        {
                            xtype: 'container',
                            flex: 1,
                            height: 60,
                            padding: '0 0 5 0',
                            layout: {
                                type: 'hbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'textareafield',
                                    flex: 1,
                                    id: 'ProtocolNewDescription',
                                    itemId: 'ProtocolNewDescription',
                                    padding: '0 5 0 0',
                                    fieldLabel: 'Oggetto',
                                    labelAlign: 'right',
                                    name: 'description'
                                },
                                {
                                    xtype: 'textareafield',
                                    flex: 1,
                                    id: 'ProtocolNewNote',
                                    itemId: 'ProtocolNewNote',
                                    padding: '0 0 0 5',
                                    fieldLabel: 'Note',
                                    labelAlign: 'right',
                                    name: 'note'
                                }
                            ]
                        },
                        {
                            xtype: 'container',
                            flex: 1,
                            padding: '0 0 5 0',
                            layout: {
                                type: 'hbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'textfield',
                                    flex: 1,
                                    id: 'ProtocolNewAct',
                                    itemId: 'ProtocolNewAct',
                                    padding: '0 5 0 0',
                                    fieldLabel: 'Atto',
                                    labelAlign: 'right',
                                    name: 'external_act_number'
                                },
                                {
                                    xtype: 'textfield',
                                    flex: 1,
                                    id: 'ProtocolNewDossier',
                                    itemId: 'ProtocolNewDossier',
                                    padding: '0 0 0 5',
                                    fieldLabel: 'Fascicolo',
                                    labelAlign: 'right',
                                    name: 'dossier'
                                }
                            ]
                        },
                        {
                            xtype: 'container',
                            flex: 1,
                            layout: {
                                type: 'hbox',
                                align: 'stretch',
                                pack: 'end'
                            },
                            items: [
                                {
                                    // Add combobox that take info from store Institutes
                                    xtype: 'combobox',
                                    flex: 1,
                                    fieldLabel: 'Istituto',
                                    labelAlign: 'right',
                                    name: 'institute_id',
                                    value: 1,
                                    forceSelection: true,
                                    queryMode: 'local',
                                    store: 'Institutes',
                                    valueField: 'institute_id'
                                },
                                {
                                    xtype: 'tbspacer',
                                    flex: 1
                                },
                                {
                                    xtype: 'combobox',
                                    flex: 1,
                                    fieldLabel: 'Segnatura Prot.',
                                    labelAlign: 'right',
                                    name: 'header_position',
                                    value: 'top',
                                    forceSelection: true,
                                    queryMode: 'local',
                                    store: 'ProtocolSignPositions',
                                    valueField: 'id'
                                }
                            ]
                        },
                        {
                            xtype: 'checkboxfield',
                            flex: 1,
                            hidden: true,
                            id: 'ProtocolReserverdCheck',
                            itemId: 'ProtocolReserverdCheck',
                            fieldLabel: '<b>Riservato</b>',
                            labelAlign: 'right',
                            name: 'reserved',
                            boxLabel: ''
                        },
                        {
                            xtype: 'tabpanel',
                            flex: 1,
                            height: 210,
                            items: [
                                {
                                    xtype: 'gridpanel',
                                    border: false,
                                    id: 'ProtocolNewLinkedCorrespondentsGrid',
                                    itemId: 'ProtocolNewLinkedCorrespondentsGrid',
                                    title: 'Mittenti / Destinatari',
                                    titleAlign: 'center',
                                    emptyText: 'Nessun corrispondente abbinato.',
                                    enableColumnHide: false,
                                    enableColumnMove: false,
                                    enableColumnResize: false,
                                    sortableColumns: false,
                                    store: 'ProtocolLinkedCorrespondentsForm',
                                    columns: [
                                        {
                                            xtype: 'gridcolumn',
                                            draggable: false,
                                            width: 30,
                                            resizable: false,
                                            align: 'center',
                                            dataIndex: 'correspondent_type',
                                            hideable: false
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            draggable: false,
                                            resizable: false,
                                            dataIndex: 'title',
                                            hideable: false,
                                            text: 'Nome',
                                            flex: 1
                                        }
                                    ],
                                    dockedItems: [
                                        {
                                            xtype: 'toolbar',
                                            dock: 'top',
                                            items: [
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        Ext.widget('ProtocolProtocolNewLinkedCorrespondentsPickerWin').show();

                                                        Ext.getStore('ProtocolCorrespondentsForm').load();
                                                    },
                                                    iconCls: 'icon-tags_grey',
                                                    text: 'Abbina Mittenti / Destinatari'
                                                },
                                                {
                                                    xtype: 'tbfill'
                                                },
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        Ext.widget('ProtocolCorrespondentEditWin').createFrom = 'P';
                                                        Ext.getCmp('ProtocolCorrespondentEditWin').show();
                                                    },
                                                    id: 'ProtocolProtocolNewCorrespondentNewBtn',
                                                    itemId: 'ProtocolProtocolNewCorrespondentNewBtn',
                                                    iconCls: 'icon-add',
                                                    text: 'Crea nuovo'
                                                }
                                            ]
                                        }
                                    ],
                                    tabConfig: {
                                        xtype: 'tab',
                                        flex: 1,
                                        iconCls: 'icon-group_go',
                                        textAlign: 'left'
                                    }
                                },
                                {
                                    xtype: 'gridpanel',
                                    border: false,
                                    id: 'ProtocolNewLinkedDocumentsGrid',
                                    itemId: 'ProtocolNewLinkedDocumentsGrid',
                                    title: 'Documenti',
                                    titleAlign: 'center',
                                    emptyText: 'Nessun documento allegato.',
                                    enableColumnHide: false,
                                    enableColumnMove: false,
                                    enableColumnResize: false,
                                    sortableColumns: false,
                                    store: 'ProtocolLinkedDocumentsForm',
                                    columns: [
                                        {
                                            xtype: 'gridcolumn',
                                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                return '<a href="/mc2-api/archive/document_file/' + record.get('id') + '" target="_blank">' + value + '</a>';
                                            },
                                            draggable: false,
                                            resizable: false,
                                            dataIndex: 'filename',
                                            hideable: false,
                                            text: 'Nome',
                                            flex: 1
                                        },
                                        {
                                            xtype: 'actioncolumn',
                                            width: 25,
                                            align: 'center',
                                            items: [
                                                {
                                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                                        Ext.getCmp('ProtocolNewLinkedDocumentsGrid').getStore().remove(record);
                                                    },
                                                    iconCls: 'icon-delete'
                                                }
                                            ]
                                        }
                                    ],
                                    dockedItems: [
                                        {
                                            xtype: 'toolbar',
                                            dock: 'top',
                                            id: 'ProtocolDocumentToolBar',
                                            items: [
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        var attachments = Ext.getStore('ProtocolLinkedDocumentsForm').getRange();

                                                        Ext.widget('ProtocolProtocolNewLinkedDocumentsPickerWin').show();
                                                        Ext.getStore('ProtocolDocumentsForm').load({
                                                            callback: function(records, operation, success) {
                                                                if (success) {
                                                                    Ext.getCmp('ProtocolProtocolNewLinkedDocumentsPickerGrid').getSelectionModel().select(attachments);
                                                                }
                                                            }
                                                        });
                                                    },
                                                    hidden: true,
                                                    iconCls: 'icon-attach',
                                                    text: 'Allega Documenti'
                                                },
                                                {
                                                    xtype: 'tbfill'
                                                },
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        Ext.widget('ArchiveDocumentUploadWin').archiveFrom = 'P';
                                                        Ext.getCmp('ArchiveDocumentUploadWin').show();
                                                    },
                                                    hidden: true,
                                                    id: 'ProtocolProtocolNewArchiveNewBtn',
                                                    itemId: 'ProtocolProtocolNewArchiveNewBtn',
                                                    iconCls: 'icon-add',
                                                    text: 'Archivia nuovo'
                                                },
                                                {
                                                    xtype: 'form',
                                                    id: 'UploadFromProtocolFrm',
                                                    itemId: 'UploadFromProtocolFrm',
                                                    layout: 'fit',
                                                    url: '/mc2-api/archive/document_file',
                                                    items: [
                                                        {
                                                            xtype: 'filefield',
                                                            fieldLabel: '',
                                                            hideLabel: true,
                                                            name: 'file',
                                                            buttonOnly: true,
                                                            buttonText: 'Carica nuovo',
                                                            listeners: {
                                                                change: {
                                                                    fn: me.onFilefieldChange,
                                                                    scope: me
                                                                }
                                                            }
                                                        },
                                                        {
                                                            xtype: 'hiddenfield',
                                                            fieldLabel: 'Label',
                                                            name: 'from',
                                                            value: 'PRT'
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ],
                                    tabConfig: {
                                        xtype: 'tab',
                                        flex: 1,
                                        iconCls: 'icon-page_white',
                                        textAlign: 'left'
                                    }
                                },
                                {
                                    xtype: 'gridpanel',
                                    border: false,
                                    id: 'ProtocolNewLinkedProtocolsGrid',
                                    itemId: 'ProtocolNewLinkedProtocolsGrid',
                                    title: 'Protocolli',
                                    titleAlign: 'center',
                                    emptyText: 'Nessun protocollo collegato.',
                                    enableColumnHide: false,
                                    enableColumnMove: false,
                                    enableColumnResize: false,
                                    sortableColumns: false,
                                    store: 'ProtocolLinkedProtocolsForm',
                                    columns: [
                                        {
                                            xtype: 'gridcolumn',
                                            draggable: false,
                                            width: 75,
                                            resizable: false,
                                            align: 'right',
                                            dataIndex: 'protocol_number',
                                            hideable: false,
                                            text: 'Numero'
                                        },
                                        {
                                            xtype: 'datecolumn',
                                            draggable: false,
                                            width: 80,
                                            resizable: false,
                                            align: 'center',
                                            dataIndex: 'date',
                                            hideable: false,
                                            text: 'Data',
                                            format: 'd/m/Y'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            draggable: false,
                                            resizable: false,
                                            dataIndex: 'type_text',
                                            hideable: false,
                                            text: 'Titolario'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            draggable: false,
                                            resizable: false,
                                            dataIndex: 'description',
                                            hideable: false,
                                            text: 'Oggetto',
                                            flex: 1
                                        }
                                    ],
                                    dockedItems: [
                                        {
                                            xtype: 'toolbar',
                                            dock: 'top',
                                            items: [
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        var links = Ext.getStore('ProtocolLinkedProtocolsForm').getRange();

                                                        Ext.widget('ProtocolProtocolNewLinkedProtocolsPickerWin').show();
                                                        Ext.getStore('ProtocolProtocolsForm').load({
                                                            callback: function(records, operation, success) {
                                                                if (success) {
                                                                    Ext.getCmp('ProtocolProtocolNewLinkedProtocolsPickerGrid').getSelectionModel().select(links);
                                                                }
                                                            }
                                                        });
                                                    },
                                                    iconCls: 'icon-link',
                                                    text: 'Collega Protocolli'
                                                }
                                            ]
                                        }
                                    ],
                                    tabConfig: {
                                        xtype: 'tab',
                                        flex: 1,
                                        iconCls: 'icon-folder',
                                        textAlign: 'left'
                                    }
                                }
                            ]
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            flex: 1,
                            dock: 'top',
                            items: function() {
                                let my_items = [];

                                my_items.push({
                                    xtype: 'button',
                                    formBind: true,
                                    iconCls: 'icon-disk',
                                    text: 'Salva',
                                    listeners: {
                                        click: {
                                            fn: me.onButtonClick,
                                            delay: 300,
                                            buffer: 1000,
                                            scope: me
                                        }
                                    }
                                });
                                if(mc2ui.app.settings.areaArchive) {
                                    my_items.push({
                                        xtype: 'button',
                                        formBind: true,
                                        hidden: true,
                                        id: 'ProtocolAddCompleteBtn',
                                        itemId: 'ProtocolAddCompleteBtn',
                                        iconCls: 'icon-accept',
                                        text: 'Salva e completa',
                                        listeners: {
                                            click: {
                                                fn: me.onButtonClick1,
                                                delay: 300,
                                                buffer: 1000,
                                                scope: me
                                            }
                                        }
                                    });
                                }

                            return my_items;
                            }()
                        }
                    ]
                }
            ],
            listeners: {
                close: {
                    fn: me.onProtocolProtocolNewWinClose,
                    scope: me
                },
                boxready: {
                    fn: me.onProtocolProtocolNewWinBoxReady,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onFilefieldChange: function(filefield, value, eOpts) {
        Ext.getCmp('UploadFromProtocolFrm').submit({
            success: function(form, action) {
                var r = Ext.decode(action.response.responseText);
                Ext.getCmp('ProtocolNewLinkedDocumentsGrid').getStore().add(r.results);
            }
        });
    },

    onButtonClick: function(button, e, eOpts) {
        var data = Ext.getCmp('ProtocolNewForm').getForm().getValues(),
            corrs = Ext.getStore('ProtocolLinkedCorrespondentsForm').getRange(),
            docs = Ext.getStore('ProtocolLinkedDocumentsForm').getRange(),
            prots = Ext.getStore('ProtocolLinkedProtocolsForm').getRange(),
            newProtocol = data.id === "",
            forced = {},
            ld = [],
            lc = [];



        Ext.getCmp('ProtocolProtocolNewWin').setLoading();
        data.linked_correspondents = [];
        data.linked_documents = [];
        data.linked_protocols = [];
        data.reserved = Ext.getCmp('ProtocolReserverdCheck').getValue() ? 1 : 0;

        corrs.forEach(function(cor) {
            data.linked_correspondents.push(cor.get('id'));
        });

        docs.forEach(function(doc) {
            data.linked_documents.push(doc.get('id'));
        });

        prots.forEach(function(prot) {
            data.linked_protocols.push(prot.get('id'));
        });

        if (!newProtocol) {
            var record = Ext.getCmp('ProtocolProtocolNewWin').record;
            ld = record.get('linked_documents') ? record.get('linked_documents') : [];
            lc = record.get('linked_correspondents') ? record.get('linked_correspondents') : [];
            forced.Data = (new Date(data.date)).getTime() !== record.get('date').getTime();
            forced.OggettoD = data.description !== record.get('description') && record.get('description') !== "";
        }

        // Checks for forbidden actions
        forced.Oggetto = data.description === "";
        forced.Titolario = data.type_id === "";
        forced.Documenti = JSON.stringify(data.linked_documents) !== JSON.stringify(ld);
        forced.Corrispondenti = JSON.stringify(data.linked_correspondents) !== JSON.stringify(lc);

        if ((newProtocol && (forced.Oggetto || forced.Titolario)) || // Protocollo nuovo e c'è una forzatura di oggetto o titolario
            (!newProtocol && (forced.Oggetto || forced.OggettoD || forced.Data ||
                              forced.Documenti || forced.Corrispondenti))) {
            if (!forced.Titolario) {
                delete forced.Titolario;
            } else {
                forced.Titolario = 'Non valorizzato';
            }
            if (!forced.Documenti) {
                delete forced.Documenti;
            } else {
                forced.Documenti = 'Cambiati';
            }
            if (!forced.Corrispondenti) {
                delete forced.Corrispondenti;
            } else {
                forced.Corrispondenti = 'Cambiati';
            }

            msg = [];
            if (forced.Oggetto) {
                msg.push('Non valorizzato');
            }
            delete forced.Oggetto;

            if (!newProtocol) {
                if (forced.OggettoD) {
                    msg.push('Cambiato');
                }
                delete forced.OggettoD;
                if (!forced.Data) {
                    delete forced.Data;
                } else {
                    forced.Data = 'Cambiata';
                }
            }

            if (msg.length !== 0) {
                forced.Oggetto = msg.join(' - ');
            }

            if(!data.description){
                Ext.Msg.show({
                    title: 'INFORMAZIONE',
                    msg: 'Non è stato inserito alcun oggetto al protocollo. Continuare?',
                    buttons: Ext.Msg.YESNO,
                    fn: function(r) {
                        if (r === 'yes') {
                            Ext.getCmp('ProtocolNewForm').saveProtocol(forced, data);
                        } else {
                            Ext.getCmp('ProtocolProtocolNewWin').setLoading(false);
                        }
                    }
                });
            } else {
                Ext.getCmp('ProtocolNewForm').saveProtocol([], data);
            }
        } else {
            Ext.getCmp('ProtocolNewForm').saveProtocol([], data);
        }
    },

    onButtonClick1: function(button, e, eOpts) {
        var data = Ext.getCmp('ProtocolNewForm').getForm().getValues(),
            corrs = Ext.getStore('ProtocolLinkedCorrespondentsForm').getRange(),
            docs = Ext.getStore('ProtocolLinkedDocumentsForm').getRange(),
            prots = Ext.getStore('ProtocolLinkedProtocolsForm').getRange(),
            newProtocol = data.id === "",
            forced = {},
            ld = [],
            lc = [];

        data.linked_correspondents = [];
        data.linked_documents = [];
        data.linked_protocols = [];
        data.reserved = Ext.getCmp('ProtocolReserverdCheck').getValue() ? 1 : 0;

        corrs.forEach(function(cor) {
            data.linked_correspondents.push(cor.get('id'));
        });

        docs.forEach(function(doc) {
            data.linked_documents.push(doc.get('id'));
        });

        prots.forEach(function(prot) {
            data.linked_protocols.push(prot.get('id'));
        });

        if (!newProtocol) {
            var record = Ext.getCmp('ProtocolProtocolNewWin').record;
            ld = record.get('linked_documents') ? record.get('linked_documents') : [];
            lc = record.get('linked_correspondents') ? record.get('linked_correspondents') : [];
            forced.Data = (new Date(data.date)).getTime() !== record.get('date').getTime();
            forced.OggettoD = data.description !== record.get('description') && record.get('description') !== "";
        }

        // Checks for forbidden actions
        forced.Oggetto = data.description === "";
        forced.Titolario = data.type_id === "";
        forced.Documenti = JSON.stringify(data.linked_documents) !== JSON.stringify(ld);
        forced.Corrispondenti = JSON.stringify(data.linked_correspondents) !== JSON.stringify(lc);

        if ((newProtocol && (forced.Oggetto || forced.Titolario)) || // Protocollo nuovo e c'è una forzatura di oggetto o titolario
            (!newProtocol && (forced.Oggetto || forced.OggettoD || forced.Data ||
                              forced.Documenti || forced.Corrispondenti))) {
            if (!forced.Titolario) {
                delete forced.Titolario;
            } else {
                forced.Titolario = 'Non valorizzato';
            }
            if (!forced.Documenti) {
                delete forced.Documenti;
            } else {
                forced.Documenti = 'Cambiati';
            }
            if (!forced.Corrispondenti) {
                delete forced.Corrispondenti;
            } else {
                forced.Corrispondenti = 'Cambiati';
            }

            msg = [];
            if (forced.Oggetto) {
                msg.push('Non valorizzato');
            }
            delete forced.Oggetto;

            if (!newProtocol) {
                if (forced.OggettoD) {
                    msg.push('Cambiato');
                }
                delete forced.OggettoD;
                if (!forced.Data) {
                    delete forced.Data;
                } else {
                    forced.Data = 'Cambiata';
                }
            }

            if (msg.length !== 0) {
                forced.Oggetto = msg.join(' - ');
            }

            if(!data.description){
                Ext.Msg.show({
                    title: 'INFORMAZIONE',
                    msg: 'Non è stato inserito alcun oggetto al protocollo. Continuare?',
                    buttons: Ext.Msg.YESNO,
                    fn: function(r) {
                        if (r === 'yes') {
                            Ext.getCmp('ProtocolNewForm').saveProtocol(forced, data, true);
                        }
                    }
                });
            } else {
                Ext.getCmp('ProtocolNewForm').saveProtocol([], data, true);
            }
        } else {
            Ext.getCmp('ProtocolNewForm').saveProtocol([], data, true);
        }
    },

    onProtocolProtocolNewWinClose: function(panel, eOpts) {
        Ext.getStore('ProtocolLinkedCorrespondentsForm').removeAll();
        Ext.getStore('ProtocolLinkedDocumentsForm').removeAll();
        Ext.getStore('ProtocolLinkedProtocolsForm').removeAll();
    },

    onProtocolProtocolNewWinBoxReady: function(component, width, height, eOpts) {
        Ext.getStore('ProtocolTypesTree').load();
        Ext.getStore('ProtocolSubjectKinds').load();
        Ext.getStore('ProtocolSendMethods').load();

        if(mc2ui.app.settings.canManageReservedProtocol) {
            Ext.getCmp('ProtocolReserverdCheck').show();
        }
    }

});