/*
 * File: app/model/ProtocolProtocol.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.model.ProtocolProtocol', {
    extend: 'Ext.data.Model',

    requires: [
        'Ext.data.Field'
    ],

    fields: [
        {
            name: 'id',
            type: 'int'
        },
        {
            name: 'date',
            type: 'date'
        },
        {
            name: 'note',
            type: 'string'
        },
        {
            name: 'type_id',
            type: 'int',
            useNull: true
        },
        {
            name: 'type_text',
            type: 'string'
        },
        {
            name: 'protocol_number',
            type: 'int'
        },
        {
            name: 'direction',
            type: 'string'
        },
        {
            convert: function(v, rec) {
                if (rec.get('direction') === 'I') {
                    return 'Entrante';
                } else if (rec.get('direction') === 'O') {
                    return 'Uscente';
                } else if (rec.get('direction') === 'L') {
                    return 'Interno';
                } else {
                    return '';
                }
            },
            name: 'direction_text',
            type: 'string'
        },
        {
            name: 'description',
            type: 'string'
        },
        {
            name: 'canceled',
            type: 'boolean'
        },
        {
            name: 'external_act_number',
            type: 'string'
        },
        {
            name: 'send_method_id',
            type: 'int',
            useNull: true
        },
        {
            name: 'send_method_text',
            type: 'string'
        },
        {
            name: 'dossier',
            type: 'string'
        },
        {
            name: 'correspondents_text',
            type: 'string'
        },
        {
            name: 'linked_correspondents'
        },
        {
            name: 'linked_protocols'
        },
        {
            name: 'linked_documents'
        },
        {
            name: 'subject_kind_id',
            type: 'int',
            useNull: true
        },
        {
            name: 'subject_kind_text',
            type: 'string'
        },
        {
            name: 'reserved',
            type: 'boolean'
        },
        {
            name: 'count_correspondents',
            type: 'int'
        },
        {
            name: 'count_protocols',
            type: 'int'
        },
        {
            name: 'count_documents',
            type: 'int'
        },
        {
            name: 'locked_docs',
            type: 'boolean'
        },
        {
            name: 'header_position'
        },
        {
            name: 'mail_sending',
            type: 'date'
        },
        {
            name: 'mechan_code',
            type: 'string'
        }
    ]
});